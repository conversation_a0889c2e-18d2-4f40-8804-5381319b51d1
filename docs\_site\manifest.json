{"source_base_path": "Q:/src/hot-preview/docs", "xrefmap": "xrefmap.yml", "files": [{"type": "Resource", "output": {"resource": {"relative_path": "index.json"}}}, {"type": "Resource", "source_relative_path": "../images/another-icon.jpeg", "output": {"resource": {"relative_path": "images/another-icon.jpeg"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/canva-abstract-e.svg", "output": {"resource": {"relative_path": "images/canva-abstract-e.svg"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/example-framework-icon.png", "output": {"resource": {"relative_path": "images/example-framework-icon.png"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/example-framework-icon.svg", "output": {"resource": {"relative_path": "images/example-framework-icon.svg"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/hot-preview-breakout.gif", "output": {"resource": {"relative_path": "images/hot-preview-breakout.gif"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/hot-preview-breakout.mp4", "output": {"resource": {"relative_path": "images/hot-preview-breakout.mp4"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/p-logo copy.svg", "output": {"resource": {"relative_path": "images/p-logo copy.svg"}}, "version": ""}, {"type": "Resource", "source_relative_path": "../images/p-logo.svg", "output": {"resource": {"relative_path": "images/p-logo.svg"}}, "version": ""}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.AutoGeneratePreviewAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.AutoGeneratePreviewAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.AutoGeneratePreviewAttribute", "Summary": "<p sourcefile=\"api/HotPreview.AutoGeneratePreviewAttribute.yml\" sourcestartlinenumber=\"1\">Controls whether auto-generated previews should be created for a UI component.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.ControlUIComponentBaseTypeAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.ControlUIComponentBaseTypeAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.ControlUIComponentBaseTypeAttribute", "Summary": "<p sourcefile=\"api/HotPreview.ControlUIComponentBaseTypeAttribute.yml\" sourcestartlinenumber=\"1\">Specifies the base type for control UI components on a specific platform.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PageUIComponentBaseTypeAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PageUIComponentBaseTypeAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PageUIComponentBaseTypeAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PageUIComponentBaseTypeAttribute.yml\" sourcestartlinenumber=\"1\">Specifies the base type for page UI components on a specific platform.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewAttribute-1.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewAttribute-1.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewAttribute<TUIComponent>", "Summary": "<p sourcefile=\"api/HotPreview.PreviewAttribute-1.yml\" sourcestartlinenumber=\"1\">Specifies that this static method creates a preview for a UI component with an explicitly specified UI component\ntype.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PreviewAttribute.yml\" sourcestartlinenumber=\"1\">Specifies that this static method creates a preview for a UI component.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewCommandAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewCommandAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewCommandAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PreviewCommandAttribute.yml\" sourcestartlinenumber=\"1\">Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.RoutePreview-1.yml", "output": {".html": {"relative_path": "api/HotPreview.RoutePreview-1.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.RoutePreview<T>", "Summary": "<p sourcefile=\"api/HotPreview.RoutePreview-1.yml\" sourcestartlinenumber=\"1\">Represents a strongly-typed preview for a route-based navigation.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.RoutePreview.yml", "output": {".html": {"relative_path": "api/HotPreview.RoutePreview.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.RoutePreview", "Summary": "<p sourcefile=\"api/HotPreview.RoutePreview.yml\" sourcestartlinenumber=\"1\">Represents a preview for a route-based navigation.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SpecialUIComponentNames.yml", "output": {".html": {"relative_path": "api/HotPreview.SpecialUIComponentNames.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SpecialUIComponentNames", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.UIComponentAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.UIComponentAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.UIComponentAttribute", "Summary": "<p sourcefile=\"api/HotPreview.UIComponentAttribute.yml\" sourcestartlinenumber=\"1\">Specifies that this class is a UI component.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.UIComponentCategoryAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.UIComponentCategoryAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.UIComponentCategoryAttribute", "Summary": "<p sourcefile=\"api/HotPreview.UIComponentCategoryAttribute.yml\" sourcestartlinenumber=\"1\">Specifies the category name for a set of UI components.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.yml", "output": {".html": {"relative_path": "api/HotPreview.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview", "Summary": null}, {"type": "Toc", "source_relative_path": "api/toc.yml", "output": {".html": {"relative_path": "api/toc.html"}, ".json": {"relative_path": "api/toc.json"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/architecture.md", "output": {".html": {"relative_path": "docs/architecture.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/attributes.md", "output": {".html": {"relative_path": "docs/attributes.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/features.md", "output": {".html": {"relative_path": "docs/features.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/getting-started.md", "output": {".html": {"relative_path": "docs/getting-started.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/samples.md", "output": {".html": {"relative_path": "docs/samples.html"}}, "version": ""}, {"type": "Toc", "source_relative_path": "docs/toc.yml", "output": {".html": {"relative_path": "docs/toc.html"}, ".json": {"relative_path": "docs/toc.json"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "index.md", "output": {".html": {"relative_path": "index.html"}}, "version": ""}, {"type": "Toc", "source_relative_path": "toc.yml", "output": {".html": {"relative_path": "toc.html"}, ".json": {"relative_path": "toc.json"}}, "version": ""}], "groups": [{"xrefmap": "xrefmap.yml"}]}