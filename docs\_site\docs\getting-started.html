<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Getting Started | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Getting Started | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/getting-started.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="getting-started">Getting Started</h1>

<p>This guide will help you set up HotPreview in your .NET application and create your first previews.</p>
<h2 id="installation">Installation</h2>
<h3 id="prerequisites">Prerequisites</h3>
<ul>
<li>.NET 9.0.300 SDK or later (see <code>global.json</code> in the repository root)</li>
<li>A supported .NET UI platform (currently .NET MAUI, with WPF support coming soon)</li>
</ul>
<h3 id="step-1-install-hotpreview-devtools">Step 1: Install HotPreview DevTools</h3>
<p>Install the global DevTools application:</p>
<pre><code class="lang-bash">dotnet tool install -g HotPreview.DevTools
</code></pre>
<h3 id="step-2-add-package-reference">Step 2: Add Package Reference</h3>
<p>Add the HotPreview package to your application project. We recommend only including it in Debug builds:</p>
<pre><code class="lang-xml">&lt;PackageReference Condition=&quot;$(Configuration) == 'Debug'&quot; Include=&quot;HotPreview.App.Maui&quot; Version=&quot;...&quot; /&gt;
</code></pre>
<h3 id="step-3-build-and-run">Step 3: Build and Run</h3>
<p>Build your application in Debug mode and run it:</p>
<pre><code class="lang-bash">dotnet build
# Run your application
</code></pre>
<p>When you build and run your app:</p>
<ul>
<li>The DevTools application launches automatically (if not already running)</li>
<li>Your app connects to DevTools when it starts</li>
<li>DevTools displays a tree of your UI components and previews</li>
</ul>
<h2 id="auto-generated-previews">Auto-Generated Previews</h2>
<p>HotPreview automatically creates previews for UI components that meet these criteria:</p>
<h3 id="pages">Pages</h3>
<ul>
<li>Derives (directly or indirectly) from <code>Microsoft.Maui.Controls.Page</code></li>
<li>Has a parameterless constructor OR constructor parameters that can be resolved via dependency injection</li>
</ul>
<h3 id="controls">Controls</h3>
<ul>
<li>Derives from <code>Microsoft.Maui.Controls.View</code> (but is not a page)</li>
<li>Has a parameterless constructor OR constructor parameters that can be resolved via dependency injection</li>
</ul>
<h2 id="creating-custom-previews">Creating Custom Previews</h2>
<p>Custom previews give you full control over how your components are displayed. They allow you to:</p>
<ul>
<li>Support components with complex constructor requirements</li>
<li>Provide realistic sample data</li>
<li>Create multiple previews for different states</li>
<li>Configure global app state for specific scenarios</li>
</ul>
<h3 id="basic-preview">Basic Preview</h3>
<p>Add a static method with the <code>[Preview]</code> attribute to your UI component class:</p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview]
    public static ConfirmAddressView Preview() =&gt; new(PreviewData.GetPreviewProducts(1),
        new DeliveryTypeModel(),
        new AddressModel()
        {
            StreetOne = &quot;21, Alex Davidson Avenue&quot;,
            StreetTwo = &quot;Opposite Omegatron, Vicent Quarters&quot;,
            City = &quot;Victoria Island&quot;,
            State = &quot;Lagos State&quot;
        });
#endif
</code></pre>
<h3 id="multiple-previews">Multiple Previews</h3>
<p>Create multiple previews to show different states:</p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview(&quot;Empty State&quot;)]
    public static CardView NoCards() =&gt; new(PreviewData.GetPreviewCards(0));

    [Preview(&quot;Single Card&quot;)]
    public static CardView SingleCard() =&gt; new(PreviewData.GetPreviewCards(1));

    [Preview(&quot;Multiple Cards&quot;)]
    public static CardView SixCards() =&gt; new(PreviewData.GetPreviewCards(6));
#endif
</code></pre>
<h3 id="preview-guidelines">Preview Guidelines</h3>
<ol>
<li><strong>Use conditional compilation</strong>: Wrap preview code in <code>#if PREVIEWS</code> to exclude it from release builds</li>
<li><strong>Provide meaningful names</strong>: Use descriptive names for multiple previews</li>
<li><strong>Use sample data</strong>: Create realistic test data to showcase your components</li>
<li><strong>Location flexibility</strong>: Preview methods can be in any class, but by convention are placed in the component class</li>
</ol>
<h2 id="navigation-and-testing">Navigation and Testing</h2>
<p>Once your app is running with DevTools:</p>
<ol>
<li><strong>Browse Components</strong>: Use the DevTools tree view to explore your UI components</li>
<li><strong>Navigate Instantly</strong>: Click any component or preview to navigate directly to it in your app</li>
<li><strong>Test States</strong>: Use multiple previews to quickly test different data states</li>
<li><strong>Cross-Platform</strong>: Run your app on different platforms and compare side-by-side</li>
</ol>
<h2 id="best-practices">Best Practices</h2>
<ul>
<li><strong>Conditional Builds</strong>: Always use <code>#if PREVIEWS</code> for preview code</li>
<li><strong>Sample Data</strong>: Create dedicated preview data classes for consistent testing</li>
<li><strong>Descriptive Names</strong>: Use clear, descriptive names for multiple previews</li>
<li><strong>Edge Cases</strong>: Create previews for empty states, error conditions, and loading states</li>
<li><strong>Component Isolation</strong>: Ensure previews work independently of app navigation state</li>
</ul>
<h2 id="next-steps">Next Steps</h2>
<ul>
<li><a href="attributes.html">Learn about all available attributes</a></li>
<li><a href="features.html">Explore advanced features</a></li>
<li><a href="../api/">Check out the API reference</a></li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/getting-started.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
