### YamlMime:XRefMap
sorted: true
references:
- uid: HotPreview
  name: HotPreview
  href: api/HotPreview.html
  commentId: N:HotPreview
  fullName: HotPreview
  nameWithType: HotPreview
- uid: HotPreview.AutoGeneratePreviewAttribute
  name: AutoGeneratePreviewAttribute
  href: api/HotPreview.AutoGeneratePreviewAttribute.html
  commentId: T:HotPreview.AutoGeneratePreviewAttribute
  fullName: HotPreview.AutoGeneratePreviewAttribute
  nameWithType: AutoGeneratePreviewAttribute
- uid: HotPreview.AutoGeneratePreviewAttribute.#ctor(System.Boolean)
  name: AutoGeneratePreviewAttribute(bool)
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute__ctor_System_Boolean_
  commentId: M:HotPreview.AutoGeneratePreviewAttribute.#ctor(System.Boolean)
  name.vb: New(Boolean)
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute(bool)
  fullName.vb: HotPreview.AutoGeneratePreviewAttribute.New(Boolean)
  nameWithType: AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute(bool)
  nameWithType.vb: AutoGeneratePreviewAttribute.New(Boolean)
- uid: HotPreview.AutoGeneratePreviewAttribute.#ctor*
  name: AutoGeneratePreviewAttribute
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute__ctor_
  commentId: Overload:HotPreview.AutoGeneratePreviewAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute
  fullName.vb: HotPreview.AutoGeneratePreviewAttribute.New
  nameWithType: AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute
  nameWithType.vb: AutoGeneratePreviewAttribute.New
- uid: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  name: AutoGenerate
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute_AutoGenerate
  commentId: P:HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  nameWithType: AutoGeneratePreviewAttribute.AutoGenerate
- uid: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate*
  name: AutoGenerate
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute_AutoGenerate_
  commentId: Overload:HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  isSpec: "True"
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  nameWithType: AutoGeneratePreviewAttribute.AutoGenerate
- uid: HotPreview.ControlUIComponentBaseTypeAttribute
  name: ControlUIComponentBaseTypeAttribute
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html
  commentId: T:HotPreview.ControlUIComponentBaseTypeAttribute
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute
  nameWithType: ControlUIComponentBaseTypeAttribute
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name: ControlUIComponentBaseTypeAttribute(string, string)
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute__ctor_System_String_System_String_
  commentId: M:HotPreview.ControlUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name.vb: New(String, String)
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute(string, string)
  fullName.vb: HotPreview.ControlUIComponentBaseTypeAttribute.New(String, String)
  nameWithType: ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute(string, string)
  nameWithType.vb: ControlUIComponentBaseTypeAttribute.New(String, String)
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.#ctor*
  name: ControlUIComponentBaseTypeAttribute
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute__ctor_
  commentId: Overload:HotPreview.ControlUIComponentBaseTypeAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute
  fullName.vb: HotPreview.ControlUIComponentBaseTypeAttribute.New
  nameWithType: ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute
  nameWithType.vb: ControlUIComponentBaseTypeAttribute.New
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  name: BaseType
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_BaseType
  commentId: P:HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  nameWithType: ControlUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType*
  name: BaseType
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_BaseType_
  commentId: Overload:HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  isSpec: "True"
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  nameWithType: ControlUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  name: Platform
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_Platform
  commentId: P:HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  nameWithType: ControlUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.Platform*
  name: Platform
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_Platform_
  commentId: Overload:HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  isSpec: "True"
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  nameWithType: ControlUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.PageUIComponentBaseTypeAttribute
  name: PageUIComponentBaseTypeAttribute
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html
  commentId: T:HotPreview.PageUIComponentBaseTypeAttribute
  fullName: HotPreview.PageUIComponentBaseTypeAttribute
  nameWithType: PageUIComponentBaseTypeAttribute
- uid: HotPreview.PageUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name: PageUIComponentBaseTypeAttribute(string, string)
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute__ctor_System_String_System_String_
  commentId: M:HotPreview.PageUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name.vb: New(String, String)
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute(string, string)
  fullName.vb: HotPreview.PageUIComponentBaseTypeAttribute.New(String, String)
  nameWithType: PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute(string, string)
  nameWithType.vb: PageUIComponentBaseTypeAttribute.New(String, String)
- uid: HotPreview.PageUIComponentBaseTypeAttribute.#ctor*
  name: PageUIComponentBaseTypeAttribute
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute__ctor_
  commentId: Overload:HotPreview.PageUIComponentBaseTypeAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute
  fullName.vb: HotPreview.PageUIComponentBaseTypeAttribute.New
  nameWithType: PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute
  nameWithType.vb: PageUIComponentBaseTypeAttribute.New
- uid: HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  name: BaseType
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_BaseType
  commentId: P:HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  nameWithType: PageUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.PageUIComponentBaseTypeAttribute.BaseType*
  name: BaseType
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_BaseType_
  commentId: Overload:HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  isSpec: "True"
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  nameWithType: PageUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.PageUIComponentBaseTypeAttribute.Platform
  name: Platform
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_Platform
  commentId: P:HotPreview.PageUIComponentBaseTypeAttribute.Platform
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.Platform
  nameWithType: PageUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.PageUIComponentBaseTypeAttribute.Platform*
  name: Platform
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_Platform_
  commentId: Overload:HotPreview.PageUIComponentBaseTypeAttribute.Platform
  isSpec: "True"
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.Platform
  nameWithType: PageUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.PreviewAttribute
  name: PreviewAttribute
  href: api/HotPreview.PreviewAttribute.html
  commentId: T:HotPreview.PreviewAttribute
  fullName: HotPreview.PreviewAttribute
  nameWithType: PreviewAttribute
- uid: HotPreview.PreviewAttribute.#ctor(System.String)
  name: PreviewAttribute(string?)
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute__ctor_System_String_
  commentId: M:HotPreview.PreviewAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewAttribute.PreviewAttribute(string?)
  fullName.vb: HotPreview.PreviewAttribute.New(String)
  nameWithType: PreviewAttribute.PreviewAttribute(string?)
  nameWithType.vb: PreviewAttribute.New(String)
- uid: HotPreview.PreviewAttribute.#ctor(System.String,System.Type)
  name: PreviewAttribute(string?, Type?)
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute__ctor_System_String_System_Type_
  commentId: M:HotPreview.PreviewAttribute.#ctor(System.String,System.Type)
  name.vb: New(String, Type)
  fullName: HotPreview.PreviewAttribute.PreviewAttribute(string?, System.Type?)
  fullName.vb: HotPreview.PreviewAttribute.New(String, System.Type)
  nameWithType: PreviewAttribute.PreviewAttribute(string?, Type?)
  nameWithType.vb: PreviewAttribute.New(String, Type)
- uid: HotPreview.PreviewAttribute.#ctor*
  name: PreviewAttribute
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute__ctor_
  commentId: Overload:HotPreview.PreviewAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewAttribute.PreviewAttribute
  fullName.vb: HotPreview.PreviewAttribute.New
  nameWithType: PreviewAttribute.PreviewAttribute
  nameWithType.vb: PreviewAttribute.New
- uid: HotPreview.PreviewAttribute.DisplayName
  name: DisplayName
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_DisplayName
  commentId: P:HotPreview.PreviewAttribute.DisplayName
  fullName: HotPreview.PreviewAttribute.DisplayName
  nameWithType: PreviewAttribute.DisplayName
- uid: HotPreview.PreviewAttribute.DisplayName*
  name: DisplayName
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_DisplayName_
  commentId: Overload:HotPreview.PreviewAttribute.DisplayName
  isSpec: "True"
  fullName: HotPreview.PreviewAttribute.DisplayName
  nameWithType: PreviewAttribute.DisplayName
- uid: HotPreview.PreviewAttribute.UIComponentType
  name: UIComponentType
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_UIComponentType
  commentId: P:HotPreview.PreviewAttribute.UIComponentType
  fullName: HotPreview.PreviewAttribute.UIComponentType
  nameWithType: PreviewAttribute.UIComponentType
- uid: HotPreview.PreviewAttribute.UIComponentType*
  name: UIComponentType
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_UIComponentType_
  commentId: Overload:HotPreview.PreviewAttribute.UIComponentType
  isSpec: "True"
  fullName: HotPreview.PreviewAttribute.UIComponentType
  nameWithType: PreviewAttribute.UIComponentType
- uid: HotPreview.PreviewAttribute`1
  name: PreviewAttribute<TUIComponent>
  href: api/HotPreview.PreviewAttribute-1.html
  commentId: T:HotPreview.PreviewAttribute`1
  name.vb: PreviewAttribute(Of TUIComponent)
  fullName: HotPreview.PreviewAttribute<TUIComponent>
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent)
  nameWithType: PreviewAttribute<TUIComponent>
  nameWithType.vb: PreviewAttribute(Of TUIComponent)
- uid: HotPreview.PreviewAttribute`1.#ctor(System.String)
  name: PreviewAttribute(string?)
  href: api/HotPreview.PreviewAttribute-1.html#HotPreview_PreviewAttribute_1__ctor_System_String_
  commentId: M:HotPreview.PreviewAttribute`1.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewAttribute<TUIComponent>.PreviewAttribute(string?)
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent).New(String)
  nameWithType: PreviewAttribute<TUIComponent>.PreviewAttribute(string?)
  nameWithType.vb: PreviewAttribute(Of TUIComponent).New(String)
- uid: HotPreview.PreviewAttribute`1.#ctor*
  name: PreviewAttribute
  href: api/HotPreview.PreviewAttribute-1.html#HotPreview_PreviewAttribute_1__ctor_
  commentId: Overload:HotPreview.PreviewAttribute`1.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewAttribute<TUIComponent>.PreviewAttribute
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent).New
  nameWithType: PreviewAttribute<TUIComponent>.PreviewAttribute
  nameWithType.vb: PreviewAttribute(Of TUIComponent).New
- uid: HotPreview.PreviewCommandAttribute
  name: PreviewCommandAttribute
  href: api/HotPreview.PreviewCommandAttribute.html
  commentId: T:HotPreview.PreviewCommandAttribute
  fullName: HotPreview.PreviewCommandAttribute
  nameWithType: PreviewCommandAttribute
- uid: HotPreview.PreviewCommandAttribute.#ctor(System.String)
  name: PreviewCommandAttribute(string?)
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute__ctor_System_String_
  commentId: M:HotPreview.PreviewCommandAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewCommandAttribute.PreviewCommandAttribute(string?)
  fullName.vb: HotPreview.PreviewCommandAttribute.New(String)
  nameWithType: PreviewCommandAttribute.PreviewCommandAttribute(string?)
  nameWithType.vb: PreviewCommandAttribute.New(String)
- uid: HotPreview.PreviewCommandAttribute.#ctor*
  name: PreviewCommandAttribute
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute__ctor_
  commentId: Overload:HotPreview.PreviewCommandAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewCommandAttribute.PreviewCommandAttribute
  fullName.vb: HotPreview.PreviewCommandAttribute.New
  nameWithType: PreviewCommandAttribute.PreviewCommandAttribute
  nameWithType.vb: PreviewCommandAttribute.New
- uid: HotPreview.PreviewCommandAttribute.DisplayName
  name: DisplayName
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute_DisplayName
  commentId: P:HotPreview.PreviewCommandAttribute.DisplayName
  fullName: HotPreview.PreviewCommandAttribute.DisplayName
  nameWithType: PreviewCommandAttribute.DisplayName
- uid: HotPreview.PreviewCommandAttribute.DisplayName*
  name: DisplayName
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute_DisplayName_
  commentId: Overload:HotPreview.PreviewCommandAttribute.DisplayName
  isSpec: "True"
  fullName: HotPreview.PreviewCommandAttribute.DisplayName
  nameWithType: PreviewCommandAttribute.DisplayName
- uid: HotPreview.RoutePreview
  name: RoutePreview
  href: api/HotPreview.RoutePreview.html
  commentId: T:HotPreview.RoutePreview
  fullName: HotPreview.RoutePreview
  nameWithType: RoutePreview
- uid: HotPreview.RoutePreview.#ctor(System.String)
  name: RoutePreview(string)
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview__ctor_System_String_
  commentId: M:HotPreview.RoutePreview.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.RoutePreview.RoutePreview(string)
  fullName.vb: HotPreview.RoutePreview.New(String)
  nameWithType: RoutePreview.RoutePreview(string)
  nameWithType.vb: RoutePreview.New(String)
- uid: HotPreview.RoutePreview.#ctor*
  name: RoutePreview
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview__ctor_
  commentId: Overload:HotPreview.RoutePreview.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.RoutePreview.RoutePreview
  fullName.vb: HotPreview.RoutePreview.New
  nameWithType: RoutePreview.RoutePreview
  nameWithType.vb: RoutePreview.New
- uid: HotPreview.RoutePreview.Route
  name: Route
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview_Route
  commentId: P:HotPreview.RoutePreview.Route
  fullName: HotPreview.RoutePreview.Route
  nameWithType: RoutePreview.Route
- uid: HotPreview.RoutePreview.Route*
  name: Route
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview_Route_
  commentId: Overload:HotPreview.RoutePreview.Route
  isSpec: "True"
  fullName: HotPreview.RoutePreview.Route
  nameWithType: RoutePreview.Route
- uid: HotPreview.RoutePreview`1
  name: RoutePreview<T>
  href: api/HotPreview.RoutePreview-1.html
  commentId: T:HotPreview.RoutePreview`1
  name.vb: RoutePreview(Of T)
  fullName: HotPreview.RoutePreview<T>
  fullName.vb: HotPreview.RoutePreview(Of T)
  nameWithType: RoutePreview<T>
  nameWithType.vb: RoutePreview(Of T)
- uid: HotPreview.RoutePreview`1.#ctor(System.String)
  name: RoutePreview(string)
  href: api/HotPreview.RoutePreview-1.html#HotPreview_RoutePreview_1__ctor_System_String_
  commentId: M:HotPreview.RoutePreview`1.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.RoutePreview<T>.RoutePreview(string)
  fullName.vb: HotPreview.RoutePreview(Of T).New(String)
  nameWithType: RoutePreview<T>.RoutePreview(string)
  nameWithType.vb: RoutePreview(Of T).New(String)
- uid: HotPreview.RoutePreview`1.#ctor*
  name: RoutePreview
  href: api/HotPreview.RoutePreview-1.html#HotPreview_RoutePreview_1__ctor_
  commentId: Overload:HotPreview.RoutePreview`1.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.RoutePreview<T>.RoutePreview
  fullName.vb: HotPreview.RoutePreview(Of T).New
  nameWithType: RoutePreview<T>.RoutePreview
  nameWithType.vb: RoutePreview(Of T).New
- uid: HotPreview.SpecialUIComponentNames
  name: SpecialUIComponentNames
  href: api/HotPreview.SpecialUIComponentNames.html
  commentId: T:HotPreview.SpecialUIComponentNames
  fullName: HotPreview.SpecialUIComponentNames
  nameWithType: SpecialUIComponentNames
- uid: HotPreview.SpecialUIComponentNames.FullApp
  name: FullApp
  href: api/HotPreview.SpecialUIComponentNames.html#HotPreview_SpecialUIComponentNames_FullApp
  commentId: F:HotPreview.SpecialUIComponentNames.FullApp
  fullName: HotPreview.SpecialUIComponentNames.FullApp
  nameWithType: SpecialUIComponentNames.FullApp
- uid: HotPreview.UIComponentAttribute
  name: UIComponentAttribute
  href: api/HotPreview.UIComponentAttribute.html
  commentId: T:HotPreview.UIComponentAttribute
  fullName: HotPreview.UIComponentAttribute
  nameWithType: UIComponentAttribute
- uid: HotPreview.UIComponentAttribute.#ctor(System.String)
  name: UIComponentAttribute(string?)
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute__ctor_System_String_
  commentId: M:HotPreview.UIComponentAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.UIComponentAttribute.UIComponentAttribute(string?)
  fullName.vb: HotPreview.UIComponentAttribute.New(String)
  nameWithType: UIComponentAttribute.UIComponentAttribute(string?)
  nameWithType.vb: UIComponentAttribute.New(String)
- uid: HotPreview.UIComponentAttribute.#ctor*
  name: UIComponentAttribute
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute__ctor_
  commentId: Overload:HotPreview.UIComponentAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.UIComponentAttribute.UIComponentAttribute
  fullName.vb: HotPreview.UIComponentAttribute.New
  nameWithType: UIComponentAttribute.UIComponentAttribute
  nameWithType.vb: UIComponentAttribute.New
- uid: HotPreview.UIComponentAttribute.DisplayName
  name: DisplayName
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute_DisplayName
  commentId: P:HotPreview.UIComponentAttribute.DisplayName
  fullName: HotPreview.UIComponentAttribute.DisplayName
  nameWithType: UIComponentAttribute.DisplayName
- uid: HotPreview.UIComponentAttribute.DisplayName*
  name: DisplayName
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute_DisplayName_
  commentId: Overload:HotPreview.UIComponentAttribute.DisplayName
  isSpec: "True"
  fullName: HotPreview.UIComponentAttribute.DisplayName
  nameWithType: UIComponentAttribute.DisplayName
- uid: HotPreview.UIComponentCategoryAttribute
  name: UIComponentCategoryAttribute
  href: api/HotPreview.UIComponentCategoryAttribute.html
  commentId: T:HotPreview.UIComponentCategoryAttribute
  fullName: HotPreview.UIComponentCategoryAttribute
  nameWithType: UIComponentCategoryAttribute
- uid: HotPreview.UIComponentCategoryAttribute.#ctor(System.String,System.Type[])
  name: UIComponentCategoryAttribute(string, params Type[])
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute__ctor_System_String_System_Type___
  commentId: M:HotPreview.UIComponentCategoryAttribute.#ctor(System.String,System.Type[])
  name.vb: New(String, ParamArray Type())
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentCategoryAttribute(string, params System.Type[])
  fullName.vb: HotPreview.UIComponentCategoryAttribute.New(String, ParamArray System.Type())
  nameWithType: UIComponentCategoryAttribute.UIComponentCategoryAttribute(string, params Type[])
  nameWithType.vb: UIComponentCategoryAttribute.New(String, ParamArray Type())
- uid: HotPreview.UIComponentCategoryAttribute.#ctor*
  name: UIComponentCategoryAttribute
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute__ctor_
  commentId: Overload:HotPreview.UIComponentCategoryAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentCategoryAttribute
  fullName.vb: HotPreview.UIComponentCategoryAttribute.New
  nameWithType: UIComponentCategoryAttribute.UIComponentCategoryAttribute
  nameWithType.vb: UIComponentCategoryAttribute.New
- uid: HotPreview.UIComponentCategoryAttribute.Name
  name: Name
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_Name
  commentId: P:HotPreview.UIComponentCategoryAttribute.Name
  fullName: HotPreview.UIComponentCategoryAttribute.Name
  nameWithType: UIComponentCategoryAttribute.Name
- uid: HotPreview.UIComponentCategoryAttribute.Name*
  name: Name
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_Name_
  commentId: Overload:HotPreview.UIComponentCategoryAttribute.Name
  isSpec: "True"
  fullName: HotPreview.UIComponentCategoryAttribute.Name
  nameWithType: UIComponentCategoryAttribute.Name
- uid: HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  name: UIComponentTypes
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_UIComponentTypes
  commentId: P:HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  nameWithType: UIComponentCategoryAttribute.UIComponentTypes
- uid: HotPreview.UIComponentCategoryAttribute.UIComponentTypes*
  name: UIComponentTypes
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_UIComponentTypes_
  commentId: Overload:HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  isSpec: "True"
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  nameWithType: UIComponentCategoryAttribute.UIComponentTypes
